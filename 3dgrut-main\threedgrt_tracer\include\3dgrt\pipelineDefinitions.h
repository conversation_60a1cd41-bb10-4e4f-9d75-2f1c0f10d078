// SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
// SPDX-License-Identifier: Apache-2.0
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#pragma once

enum MOGRenderOpts {
    MOGRenderNone                   = 0,
    MOGRenderAdaptiveKernelClamping = 1 << 0,
    MOGRenderWithNormals            = 1 << 1,
    MOGRenderWithHitCounts          = 1 << 2,
    MOGRenderDefault                = MOGRenderNone
};

enum MOGPrimitiveTypes {
    MOGTracingIcosaHedron,
    MOGTracingOctraHedron,
    MOGTracingTetraHedron,
    MOGTracingDiamond,
    MOGTracingSphere,
    MOGTracingCustom,
    MOGTracingTriHexa,
    MOGTracingTriSurfel,
    MOGTracingInstances,
};

enum MOGTracingPipeline {
    MOGTracingPipelineCH    = 0,
    MOGTracingPipelineAH    = 1,
    MOGTracingPipelineIS    = 2,
    MOGTracingPipelineMLAT  = 3,
    MOGTracingPipelineMBOIT = 4,
    MOGTracingPipelineHC    = 5,
    MOGTracingPipelineInd   = 6,
};