Metadata-Version: 2.1
Name: slangtorch
Version: 1.3.4
Summary: A package for calling Slang modules from Python and PyTorch.
Project-URL: Homepage, https://github.com/shader-slang/slang
Project-URL: Bug Tracker, https://github.com/shader-slang/slang-python/issues
Author: Slang Development Team
License-File: LICENSE
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Requires-Python: >=3.7
Requires-Dist: filelock
Requires-Dist: hatchling>=1.11.0
Requires-Dist: ninja
Requires-Dist: torch>=1.1.0
Description-Content-Type: text/markdown

# Slang-Torch Package

This package allows you to use Slang as a language to write
PyTorch kernels.

Slang user guide: https://shader-slang.com/slang/user-guide/
`slangtorch` documentation: https://shader-slang.com/slang/user-guide/a1-02-slangpy.html