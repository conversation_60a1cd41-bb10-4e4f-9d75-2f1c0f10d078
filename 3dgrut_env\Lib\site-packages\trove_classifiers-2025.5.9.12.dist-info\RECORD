../../Scripts/trove-classifiers.exe,sha256=g8Ki_4yDkGhIWK8HSDCbM3ptkNRjgTo7eEFzOXMrMh0,108426
trove_classifiers-2025.5.9.12.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
trove_classifiers-2025.5.9.12.dist-info/METADATA,sha256=ucrvIRQ8XRvzKG3UKQoXvWnJqXePHvwVUvuGn4smR5c,2349
trove_classifiers-2025.5.9.12.dist-info/RECORD,,
trove_classifiers-2025.5.9.12.dist-info/WHEEL,sha256=0CuiUZ_p9E4cD6NyLD6UG80LBXYyiSYZOKDm5lp32xk,91
trove_classifiers-2025.5.9.12.dist-info/entry_points.txt,sha256=bf-LPy6zZ8JFQBU5aPNCjJrYeGvRFuIZ7NtDIR6X_DI,69
trove_classifiers-2025.5.9.12.dist-info/licenses/LICENSE,sha256=xx0jnfkXJvxRnG63LTGOxlggYnIysveWIZ6H3PNdCrQ,11357
trove_classifiers-2025.5.9.12.dist-info/top_level.txt,sha256=_TvXv_KZBUdclJPg-0rAmpTMgQDtBOjK0s12yV-hWRc,18
trove_classifiers/__init__.py,sha256=FpiRfCccu4ZlYSli6y5Nrpqju1FhghDdSudXlYq0Ma8,42839
trove_classifiers/__main__.py,sha256=4epbAiV17G5r2G8oP2O4orqW5V-9MvV2pwI7nib5SFY,177
trove_classifiers/__pycache__/__init__.cpython-312.pyc,,
trove_classifiers/__pycache__/__main__.cpython-312.pyc,,
trove_classifiers/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
