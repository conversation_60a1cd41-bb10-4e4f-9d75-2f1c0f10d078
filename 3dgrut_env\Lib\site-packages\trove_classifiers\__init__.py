from typing import Dict, List, Set

# A set of classifier names
sorted_classifiers: List[str] = [
    "Development Status :: 1 - Planning",
    "Development Status :: 2 - Pre-Alpha",
    "Development Status :: 3 - Alpha",
    "Development Status :: 4 - Beta",
    "Development Status :: 5 - Production/Stable",
    "Development Status :: 6 - Mature",
    "Development Status :: 7 - Inactive",
    "Environment :: Console",
    "Environment :: Console :: Curses",
    "Environment :: Console :: Framebuffer",
    "Environment :: Console :: Newt",
    "Environment :: Console :: svgalib",
    "Environment :: GPU",
    "Environment :: GPU :: NVIDIA CUDA",
    "Environment :: GPU :: NVIDIA CUDA :: 1.0",
    "Environment :: GPU :: NVIDIA CUDA :: 1.1",
    "Environment :: GPU :: NVIDIA CUDA :: 2.0",
    "Environment :: GPU :: NVIDIA CUDA :: 2.1",
    "Environment :: GPU :: NVIDIA CUDA :: 2.2",
    "Environment :: GPU :: NVIDIA CUDA :: 2.3",
    "Environment :: GPU :: NVIDIA CUDA :: 3.0",
    "Environment :: GPU :: NVIDIA CUDA :: 3.1",
    "Environment :: GPU :: NVIDIA CUDA :: 3.2",
    "Environment :: GPU :: NVIDIA CUDA :: 4.0",
    "Environment :: GPU :: NVIDIA CUDA :: 4.1",
    "Environment :: GPU :: NVIDIA CUDA :: 4.2",
    "Environment :: GPU :: NVIDIA CUDA :: 5.0",
    "Environment :: GPU :: NVIDIA CUDA :: 5.5",
    "Environment :: GPU :: NVIDIA CUDA :: 6.0",
    "Environment :: GPU :: NVIDIA CUDA :: 6.5",
    "Environment :: GPU :: NVIDIA CUDA :: 7.0",
    "Environment :: GPU :: NVIDIA CUDA :: 7.5",
    "Environment :: GPU :: NVIDIA CUDA :: 8.0",
    "Environment :: GPU :: NVIDIA CUDA :: 9.0",
    "Environment :: GPU :: NVIDIA CUDA :: 9.1",
    "Environment :: GPU :: NVIDIA CUDA :: 9.2",
    "Environment :: GPU :: NVIDIA CUDA :: 10.0",
    "Environment :: GPU :: NVIDIA CUDA :: 10.1",
    "Environment :: GPU :: NVIDIA CUDA :: 10.2",
    "Environment :: GPU :: NVIDIA CUDA :: 11",
    "Environment :: GPU :: NVIDIA CUDA :: 11.0",
    "Environment :: GPU :: NVIDIA CUDA :: 11.1",
    "Environment :: GPU :: NVIDIA CUDA :: 11.2",
    "Environment :: GPU :: NVIDIA CUDA :: 11.3",
    "Environment :: GPU :: NVIDIA CUDA :: 11.4",
    "Environment :: GPU :: NVIDIA CUDA :: 11.5",
    "Environment :: GPU :: NVIDIA CUDA :: 11.6",
    "Environment :: GPU :: NVIDIA CUDA :: 11.7",
    "Environment :: GPU :: NVIDIA CUDA :: 11.8",
    "Environment :: GPU :: NVIDIA CUDA :: 12",
    "Environment :: GPU :: NVIDIA CUDA :: 12 :: 12.0",
    "Environment :: GPU :: NVIDIA CUDA :: 12 :: 12.1",
    "Environment :: GPU :: NVIDIA CUDA :: 12 :: 12.2",
    "Environment :: GPU :: NVIDIA CUDA :: 12 :: 12.3",
    "Environment :: GPU :: NVIDIA CUDA :: 12 :: 12.4",
    "Environment :: GPU :: NVIDIA CUDA :: 12 :: 12.5",
    "Environment :: GPU :: NVIDIA CUDA :: 12 :: 12.6",
    "Environment :: Handhelds/PDA's",
    "Environment :: MacOS X",
    "Environment :: MacOS X :: Aqua",
    "Environment :: MacOS X :: Carbon",
    "Environment :: MacOS X :: Cocoa",
    "Environment :: No Input/Output (Daemon)",
    "Environment :: OpenStack",
    "Environment :: Other Environment",
    "Environment :: Plugins",
    "Environment :: Web Environment",
    "Environment :: Web Environment :: Buffet",
    "Environment :: Web Environment :: Mozilla",
    "Environment :: Web Environment :: ToscaWidgets",
    "Environment :: WebAssembly",
    "Environment :: WebAssembly :: Emscripten",
    "Environment :: WebAssembly :: WASI",
    "Environment :: Win32 (MS Windows)",
    "Environment :: X11 Applications",
    "Environment :: X11 Applications :: GTK",
    "Environment :: X11 Applications :: Gnome",
    "Environment :: X11 Applications :: KDE",
    "Environment :: X11 Applications :: Qt",
    "Framework :: AWS CDK",
    "Framework :: AWS CDK :: 1",
    "Framework :: AWS CDK :: 2",
    "Framework :: AiiDA",
    "Framework :: Ansible",
    "Framework :: AnyIO",
    "Framework :: Apache Airflow",
    "Framework :: Apache Airflow :: Provider",
    "Framework :: AsyncIO",
    "Framework :: BEAT",
    "Framework :: BFG",
    "Framework :: Bob",
    "Framework :: Bottle",
    "Framework :: Buildout",
    "Framework :: Buildout :: Extension",
    "Framework :: Buildout :: Recipe",
    "Framework :: CastleCMS",
    "Framework :: CastleCMS :: Theme",
    "Framework :: Celery",
    "Framework :: Chandler",
    "Framework :: CherryPy",
    "Framework :: CubicWeb",
    "Framework :: Dash",
    "Framework :: Datasette",
    "Framework :: Django",
    "Framework :: Django :: 1",
    "Framework :: Django :: 1.4",
    "Framework :: Django :: 1.5",
    "Framework :: Django :: 1.6",
    "Framework :: Django :: 1.7",
    "Framework :: Django :: 1.8",
    "Framework :: Django :: 1.9",
    "Framework :: Django :: 1.10",
    "Framework :: Django :: 1.11",
    "Framework :: Django :: 2",
    "Framework :: Django :: 2.0",
    "Framework :: Django :: 2.1",
    "Framework :: Django :: 2.2",
    "Framework :: Django :: 3",
    "Framework :: Django :: 3.0",
    "Framework :: Django :: 3.1",
    "Framework :: Django :: 3.2",
    "Framework :: Django :: 4",
    "Framework :: Django :: 4.0",
    "Framework :: Django :: 4.1",
    "Framework :: Django :: 4.2",
    "Framework :: Django :: 5",
    "Framework :: Django :: 5.0",
    "Framework :: Django :: 5.1",
    "Framework :: Django :: 5.2",
    "Framework :: Django CMS",
    "Framework :: Django CMS :: 3.4",
    "Framework :: Django CMS :: 3.5",
    "Framework :: Django CMS :: 3.6",
    "Framework :: Django CMS :: 3.7",
    "Framework :: Django CMS :: 3.8",
    "Framework :: Django CMS :: 3.9",
    "Framework :: Django CMS :: 3.10",
    "Framework :: Django CMS :: 3.11",
    "Framework :: Django CMS :: 4.0",
    "Framework :: Django CMS :: 4.1",
    "Framework :: Django CMS :: 5.0",
    "Framework :: FastAPI",
    "Framework :: Flake8",
    "Framework :: Flask",
    "Framework :: Hatch",
    "Framework :: Hypothesis",
    "Framework :: IDLE",
    "Framework :: IPython",
    "Framework :: Jupyter",
    "Framework :: Jupyter :: JupyterLab",
    "Framework :: Jupyter :: JupyterLab :: 1",
    "Framework :: Jupyter :: JupyterLab :: 2",
    "Framework :: Jupyter :: JupyterLab :: 3",
    "Framework :: Jupyter :: JupyterLab :: 4",
    "Framework :: Jupyter :: JupyterLab :: Extensions",
    "Framework :: Jupyter :: JupyterLab :: Extensions :: Mime Renderers",
    "Framework :: Jupyter :: JupyterLab :: Extensions :: Prebuilt",
    "Framework :: Jupyter :: JupyterLab :: Extensions :: Themes",
    "Framework :: Kedro",
    "Framework :: Lektor",
    "Framework :: Masonite",
    "Framework :: Matplotlib",
    "Framework :: MkDocs",
    "Framework :: Nengo",
    "Framework :: Odoo",
    "Framework :: Odoo :: 8.0",
    "Framework :: Odoo :: 9.0",
    "Framework :: Odoo :: 10.0",
    "Framework :: Odoo :: 11.0",
    "Framework :: Odoo :: 12.0",
    "Framework :: Odoo :: 13.0",
    "Framework :: Odoo :: 14.0",
    "Framework :: Odoo :: 15.0",
    "Framework :: Odoo :: 16.0",
    "Framework :: Odoo :: 17.0",
    "Framework :: Odoo :: 18.0",
    "Framework :: OpenTelemetry",
    "Framework :: OpenTelemetry :: Distros",
    "Framework :: OpenTelemetry :: Exporters",
    "Framework :: OpenTelemetry :: Instrumentations",
    "Framework :: Opps",
    "Framework :: Paste",
    "Framework :: Pelican",
    "Framework :: Pelican :: Plugins",
    "Framework :: Pelican :: Themes",
    "Framework :: Plone",
    "Framework :: Plone :: 3.2",
    "Framework :: Plone :: 3.3",
    "Framework :: Plone :: 4.0",
    "Framework :: Plone :: 4.1",
    "Framework :: Plone :: 4.2",
    "Framework :: Plone :: 4.3",
    "Framework :: Plone :: 5.0",
    "Framework :: Plone :: 5.1",
    "Framework :: Plone :: 5.2",
    "Framework :: Plone :: 5.3",
    "Framework :: Plone :: 6.0",
    "Framework :: Plone :: 6.1",
    "Framework :: Plone :: 6.2",
    "Framework :: Plone :: Addon",
    "Framework :: Plone :: Core",
    "Framework :: Plone :: Distribution",
    "Framework :: Plone :: Theme",
    "Framework :: PySimpleGUI",
    "Framework :: PySimpleGUI :: 4",
    "Framework :: PySimpleGUI :: 5",
    "Framework :: Pycsou",
    "Framework :: Pydantic",
    "Framework :: Pydantic :: 1",
    "Framework :: Pydantic :: 2",
    "Framework :: Pylons",
    "Framework :: Pyodide",
    "Framework :: Pyramid",
    "Framework :: Pytest",
    "Framework :: Review Board",
    "Framework :: Robot Framework",
    "Framework :: Robot Framework :: Library",
    "Framework :: Robot Framework :: Tool",
    "Framework :: Scrapy",
    "Framework :: Setuptools Plugin",
    "Framework :: Sphinx",
    "Framework :: Sphinx :: Domain",
    "Framework :: Sphinx :: Extension",
    "Framework :: Sphinx :: Theme",
    "Framework :: Trac",
    "Framework :: Trio",
    "Framework :: Tryton",
    "Framework :: TurboGears",
    "Framework :: TurboGears :: Applications",
    "Framework :: TurboGears :: Widgets",
    "Framework :: Twisted",
    "Framework :: Wagtail",
    "Framework :: Wagtail :: 1",
    "Framework :: Wagtail :: 2",
    "Framework :: Wagtail :: 3",
    "Framework :: Wagtail :: 4",
    "Framework :: Wagtail :: 5",
    "Framework :: Wagtail :: 6",
    "Framework :: Wagtail :: 7",
    "Framework :: ZODB",
    "Framework :: Zope",
    "Framework :: Zope2",
    "Framework :: Zope3",
    "Framework :: Zope :: 2",
    "Framework :: Zope :: 3",
    "Framework :: Zope :: 4",
    "Framework :: Zope :: 5",
    "Framework :: aiohttp",
    "Framework :: cocotb",
    "Framework :: napari",
    "Framework :: tox",
    "Intended Audience :: Customer Service",
    "Intended Audience :: Developers",
    "Intended Audience :: Education",
    "Intended Audience :: End Users/Desktop",
    "Intended Audience :: Financial and Insurance Industry",
    "Intended Audience :: Healthcare Industry",
    "Intended Audience :: Information Technology",
    "Intended Audience :: Legal Industry",
    "Intended Audience :: Manufacturing",
    "Intended Audience :: Other Audience",
    "Intended Audience :: Religion",
    "Intended Audience :: Science/Research",
    "Intended Audience :: System Administrators",
    "Intended Audience :: Telecommunications Industry",
    "License :: Aladdin Free Public License (AFPL)",
    "License :: CC0 1.0 Universal (CC0 1.0) Public Domain Dedication",
    "License :: CeCILL-B Free Software License Agreement (CECILL-B)",
    "License :: CeCILL-C Free Software License Agreement (CECILL-C)",
    "License :: DFSG approved",
    "License :: Eiffel Forum License (EFL)",
    "License :: Free For Educational Use",
    "License :: Free For Home Use",
    "License :: Free To Use But Restricted",
    "License :: Free for non-commercial use",
    "License :: Freely Distributable",
    "License :: Freeware",
    "License :: GUST Font License 1.0",
    "License :: GUST Font License 2006-09-30",
    "License :: Netscape Public License (NPL)",
    "License :: Nokia Open Source License (NOKOS)",
    "License :: OSI Approved",
    "License :: OSI Approved :: Academic Free License (AFL)",
    "License :: OSI Approved :: Apache Software License",
    "License :: OSI Approved :: Apple Public Source License",
    "License :: OSI Approved :: Artistic License",
    "License :: OSI Approved :: Attribution Assurance License",
    "License :: OSI Approved :: BSD License",
    "License :: OSI Approved :: Blue Oak Model License (BlueOak-1.0.0)",
    "License :: OSI Approved :: Boost Software License 1.0 (BSL-1.0)",
    "License :: OSI Approved :: CEA CNRS Inria Logiciel Libre License, version 2.1 (CeCILL-2.1)",
    "License :: OSI Approved :: CMU License (MIT-CMU)",
    "License :: OSI Approved :: Common Development and Distribution License 1.0 (CDDL-1.0)",
    "License :: OSI Approved :: Common Public License",
    "License :: OSI Approved :: Eclipse Public License 1.0 (EPL-1.0)",
    "License :: OSI Approved :: Eclipse Public License 2.0 (EPL-2.0)",
    "License :: OSI Approved :: Educational Community License, Version 2.0 (ECL-2.0)",
    "License :: OSI Approved :: Eiffel Forum License",
    "License :: OSI Approved :: European Union Public Licence 1.0 (EUPL 1.0)",
    "License :: OSI Approved :: European Union Public Licence 1.1 (EUPL 1.1)",
    "License :: OSI Approved :: European Union Public Licence 1.2 (EUPL 1.2)",
    "License :: OSI Approved :: GNU Affero General Public License v3",
    "License :: OSI Approved :: GNU Affero General Public License v3 or later (AGPLv3+)",
    "License :: OSI Approved :: GNU Free Documentation License (FDL)",
    "License :: OSI Approved :: GNU General Public License (GPL)",
    "License :: OSI Approved :: GNU General Public License v2 (GPLv2)",
    "License :: OSI Approved :: GNU General Public License v2 or later (GPLv2+)",
    "License :: OSI Approved :: GNU General Public License v3 (GPLv3)",
    "License :: OSI Approved :: GNU General Public License v3 or later (GPLv3+)",
    "License :: OSI Approved :: GNU Lesser General Public License v2 (LGPLv2)",
    "License :: OSI Approved :: GNU Lesser General Public License v2 or later (LGPLv2+)",
    "License :: OSI Approved :: GNU Lesser General Public License v3 (LGPLv3)",
    "License :: OSI Approved :: GNU Lesser General Public License v3 or later (LGPLv3+)",
    "License :: OSI Approved :: GNU Library or Lesser General Public License (LGPL)",
    "License :: OSI Approved :: Historical Permission Notice and Disclaimer (HPND)",
    "License :: OSI Approved :: IBM Public License",
    "License :: OSI Approved :: ISC License (ISCL)",
    "License :: OSI Approved :: MIT License",
    "License :: OSI Approved :: MIT No Attribution License (MIT-0)",
    "License :: OSI Approved :: MirOS License (MirOS)",
    "License :: OSI Approved :: Motosoto License",
    "License :: OSI Approved :: Mozilla Public License 1.0 (MPL)",
    "License :: OSI Approved :: Mozilla Public License 1.1 (MPL 1.1)",
    "License :: OSI Approved :: Mozilla Public License 2.0 (MPL 2.0)",
    "License :: OSI Approved :: Mulan Permissive Software License v2 (MulanPSL-2.0)",
    "License :: OSI Approved :: NASA Open Source Agreement v1.3 (NASA-1.3)",
    "License :: OSI Approved :: Nethack General Public License",
    "License :: OSI Approved :: Nokia Open Source License",
    "License :: OSI Approved :: Open Group Test Suite License",
    "License :: OSI Approved :: Open Software License 3.0 (OSL-3.0)",
    "License :: OSI Approved :: PostgreSQL License",
    "License :: OSI Approved :: Python License (CNRI Python License)",
    "License :: OSI Approved :: Python Software Foundation License",
    "License :: OSI Approved :: Qt Public License (QPL)",
    "License :: OSI Approved :: Ricoh Source Code Public License",
    "License :: OSI Approved :: SIL Open Font License 1.1 (OFL-1.1)",
    "License :: OSI Approved :: Sleepycat License",
    "License :: OSI Approved :: Sun Public License",
    "License :: OSI Approved :: The Unlicense (Unlicense)",
    "License :: OSI Approved :: Universal Permissive License (UPL)",
    "License :: OSI Approved :: University of Illinois/NCSA Open Source License",
    "License :: OSI Approved :: Vovida Software License 1.0",
    "License :: OSI Approved :: W3C License",
    "License :: OSI Approved :: Zero-Clause BSD (0BSD)",
    "License :: OSI Approved :: Zope Public License",
    "License :: OSI Approved :: zlib/libpng License",
    "License :: Other/Proprietary License",
    "License :: Public Domain",
    "License :: Repoze Public License",
    "Natural Language :: Afrikaans",
    "Natural Language :: Arabic",
    "Natural Language :: Armenian",
    "Natural Language :: Basque",
    "Natural Language :: Bengali",
    "Natural Language :: Bosnian",
    "Natural Language :: Bulgarian",
    "Natural Language :: Cantonese",
    "Natural Language :: Catalan",
    "Natural Language :: Catalan (Valencian)",
    "Natural Language :: Chinese (Simplified)",
    "Natural Language :: Chinese (Traditional)",
    "Natural Language :: Croatian",
    "Natural Language :: Czech",
    "Natural Language :: Danish",
    "Natural Language :: Dutch",
    "Natural Language :: English",
    "Natural Language :: Esperanto",
    "Natural Language :: Estonian",
    "Natural Language :: Finnish",
    "Natural Language :: French",
    "Natural Language :: Galician",
    "Natural Language :: Georgian",
    "Natural Language :: German",
    "Natural Language :: Greek",
    "Natural Language :: Hebrew",
    "Natural Language :: Hindi",
    "Natural Language :: Hungarian",
    "Natural Language :: Icelandic",
    "Natural Language :: Indonesian",
    "Natural Language :: Irish",
    "Natural Language :: Italian",
    "Natural Language :: Japanese",
    "Natural Language :: Javanese",
    "Natural Language :: Korean",
    "Natural Language :: Latin",
    "Natural Language :: Latvian",
    "Natural Language :: Lithuanian",
    "Natural Language :: Macedonian",
    "Natural Language :: Malay",
    "Natural Language :: Marathi",
    "Natural Language :: Nepali",
    "Natural Language :: Norwegian",
    "Natural Language :: Panjabi",
    "Natural Language :: Persian",
    "Natural Language :: Polish",
    "Natural Language :: Portuguese",
    "Natural Language :: Portuguese (Brazilian)",
    "Natural Language :: Romanian",
    "Natural Language :: Russian",
    "Natural Language :: Serbian",
    "Natural Language :: Slovak",
    "Natural Language :: Slovenian",
    "Natural Language :: Spanish",
    "Natural Language :: Swedish",
    "Natural Language :: Tamil",
    "Natural Language :: Telugu",
    "Natural Language :: Thai",
    "Natural Language :: Tibetan",
    "Natural Language :: Turkish",
    "Natural Language :: Ukrainian",
    "Natural Language :: Urdu",
    "Natural Language :: Vietnamese",
    "Natural Language :: Yiddish",
    "Operating System :: Android",
    "Operating System :: BeOS",
    "Operating System :: MacOS",
    "Operating System :: MacOS :: MacOS 9",
    "Operating System :: MacOS :: MacOS X",
    "Operating System :: Microsoft",
    "Operating System :: Microsoft :: MS-DOS",
    "Operating System :: Microsoft :: Windows",
    "Operating System :: Microsoft :: Windows :: Windows 3.1 or Earlier",
    "Operating System :: Microsoft :: Windows :: Windows 7",
    "Operating System :: Microsoft :: Windows :: Windows 8",
    "Operating System :: Microsoft :: Windows :: Windows 8.1",
    "Operating System :: Microsoft :: Windows :: Windows 10",
    "Operating System :: Microsoft :: Windows :: Windows 11",
    "Operating System :: Microsoft :: Windows :: Windows 95/98/2000",
    "Operating System :: Microsoft :: Windows :: Windows CE",
    "Operating System :: Microsoft :: Windows :: Windows NT/2000",
    "Operating System :: Microsoft :: Windows :: Windows Server 2003",
    "Operating System :: Microsoft :: Windows :: Windows Server 2008",
    "Operating System :: Microsoft :: Windows :: Windows Vista",
    "Operating System :: Microsoft :: Windows :: Windows XP",
    "Operating System :: OS Independent",
    "Operating System :: OS/2",
    "Operating System :: Other OS",
    "Operating System :: PDA Systems",
    "Operating System :: POSIX",
    "Operating System :: POSIX :: AIX",
    "Operating System :: POSIX :: BSD",
    "Operating System :: POSIX :: BSD :: BSD/OS",
    "Operating System :: POSIX :: BSD :: FreeBSD",
    "Operating System :: POSIX :: BSD :: NetBSD",
    "Operating System :: POSIX :: BSD :: OpenBSD",
    "Operating System :: POSIX :: GNU Hurd",
    "Operating System :: POSIX :: HP-UX",
    "Operating System :: POSIX :: IRIX",
    "Operating System :: POSIX :: Linux",
    "Operating System :: POSIX :: Other",
    "Operating System :: POSIX :: SCO",
    "Operating System :: POSIX :: SunOS/Solaris",
    "Operating System :: PalmOS",
    "Operating System :: RISC OS",
    "Operating System :: Unix",
    "Operating System :: iOS",
    "Programming Language :: APL",
    "Programming Language :: ASP",
    "Programming Language :: Ada",
    "Programming Language :: Assembly",
    "Programming Language :: Awk",
    "Programming Language :: Basic",
    "Programming Language :: C",
    "Programming Language :: C#",
    "Programming Language :: C++",
    "Programming Language :: Cold Fusion",
    "Programming Language :: Cython",
    "Programming Language :: D",
    "Programming Language :: Delphi/Kylix",
    "Programming Language :: Dylan",
    "Programming Language :: Eiffel",
    "Programming Language :: Emacs-Lisp",
    "Programming Language :: Erlang",
    "Programming Language :: Euler",
    "Programming Language :: Euphoria",
    "Programming Language :: F#",
    "Programming Language :: Forth",
    "Programming Language :: Fortran",
    "Programming Language :: Go",
    "Programming Language :: Haskell",
    "Programming Language :: Hy",
    "Programming Language :: Java",
    "Programming Language :: JavaScript",
    "Programming Language :: Kotlin",
    "Programming Language :: Lisp",
    "Programming Language :: Logo",
    "Programming Language :: Lua",
    "Programming Language :: ML",
    "Programming Language :: Modula",
    "Programming Language :: OCaml",
    "Programming Language :: Object Pascal",
    "Programming Language :: Objective C",
    "Programming Language :: Other",
    "Programming Language :: Other Scripting Engines",
    "Programming Language :: PHP",
    "Programming Language :: PL/SQL",
    "Programming Language :: PROGRESS",
    "Programming Language :: Pascal",
    "Programming Language :: Perl",
    "Programming Language :: Pike",
    "Programming Language :: Pliant",
    "Programming Language :: Prolog",
    "Programming Language :: Python",
    "Programming Language :: Python :: 2",
    "Programming Language :: Python :: 2 :: Only",
    "Programming Language :: Python :: 2.3",
    "Programming Language :: Python :: 2.4",
    "Programming Language :: Python :: 2.5",
    "Programming Language :: Python :: 2.6",
    "Programming Language :: Python :: 2.7",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3 :: Only",
    "Programming Language :: Python :: 3.0",
    "Programming Language :: Python :: 3.1",
    "Programming Language :: Python :: 3.2",
    "Programming Language :: Python :: 3.3",
    "Programming Language :: Python :: 3.4",
    "Programming Language :: Python :: 3.5",
    "Programming Language :: Python :: 3.6",
    "Programming Language :: Python :: 3.7",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "Programming Language :: Python :: 3.14",
    "Programming Language :: Python :: 3.15",
    "Programming Language :: Python :: Free Threading",
    "Programming Language :: Python :: Free Threading :: 1 - Unstable",
    "Programming Language :: Python :: Free Threading :: 2 - Beta",
    "Programming Language :: Python :: Free Threading :: 3 - Stable",
    "Programming Language :: Python :: Free Threading :: 4 - Resilient",
    "Programming Language :: Python :: Implementation",
    "Programming Language :: Python :: Implementation :: CPython",
    "Programming Language :: Python :: Implementation :: GraalPy",
    "Programming Language :: Python :: Implementation :: IronPython",
    "Programming Language :: Python :: Implementation :: Jython",
    "Programming Language :: Python :: Implementation :: MicroPython",
    "Programming Language :: Python :: Implementation :: PyPy",
    "Programming Language :: Python :: Implementation :: Stackless",
    "Programming Language :: R",
    "Programming Language :: REBOL",
    "Programming Language :: Rexx",
    "Programming Language :: Ruby",
    "Programming Language :: Rust",
    "Programming Language :: SQL",
    "Programming Language :: Scheme",
    "Programming Language :: Simula",
    "Programming Language :: Smalltalk",
    "Programming Language :: Tcl",
    "Programming Language :: Unix Shell",
    "Programming Language :: Visual Basic",
    "Programming Language :: XBasic",
    "Programming Language :: YACC",
    "Programming Language :: Zope",
    "Topic :: Adaptive Technologies",
    "Topic :: Artistic Software",
    "Topic :: Communications",
    "Topic :: Communications :: BBS",
    "Topic :: Communications :: Chat",
    "Topic :: Communications :: Chat :: ICQ",
    "Topic :: Communications :: Chat :: Internet Relay Chat",
    "Topic :: Communications :: Chat :: Unix Talk",
    "Topic :: Communications :: Conferencing",
    "Topic :: Communications :: Email",
    "Topic :: Communications :: Email :: Address Book",
    "Topic :: Communications :: Email :: Email Clients (MUA)",
    "Topic :: Communications :: Email :: Filters",
    "Topic :: Communications :: Email :: Mail Transport Agents",
    "Topic :: Communications :: Email :: Mailing List Servers",
    "Topic :: Communications :: Email :: Post-Office",
    "Topic :: Communications :: Email :: Post-Office :: IMAP",
    "Topic :: Communications :: Email :: Post-Office :: POP3",
    "Topic :: Communications :: FIDO",
    "Topic :: Communications :: Fax",
    "Topic :: Communications :: File Sharing",
    "Topic :: Communications :: File Sharing :: Gnutella",
    "Topic :: Communications :: File Sharing :: Napster",
    "Topic :: Communications :: Ham Radio",
    "Topic :: Communications :: Internet Phone",
    "Topic :: Communications :: Telephony",
    "Topic :: Communications :: Usenet News",
    "Topic :: Database",
    "Topic :: Database :: Database Engines/Servers",
    "Topic :: Database :: Front-Ends",
    "Topic :: Desktop Environment",
    "Topic :: Desktop Environment :: File Managers",
    "Topic :: Desktop Environment :: GNUstep",
    "Topic :: Desktop Environment :: Gnome",
    "Topic :: Desktop Environment :: K Desktop Environment (KDE)",
    "Topic :: Desktop Environment :: K Desktop Environment (KDE) :: Themes",
    "Topic :: Desktop Environment :: PicoGUI",
    "Topic :: Desktop Environment :: PicoGUI :: Applications",
    "Topic :: Desktop Environment :: PicoGUI :: Themes",
    "Topic :: Desktop Environment :: Screen Savers",
    "Topic :: Desktop Environment :: Window Managers",
    "Topic :: Desktop Environment :: Window Managers :: Afterstep",
    "Topic :: Desktop Environment :: Window Managers :: Afterstep :: Themes",
    "Topic :: Desktop Environment :: Window Managers :: Applets",
    "Topic :: Desktop Environment :: Window Managers :: Blackbox",
    "Topic :: Desktop Environment :: Window Managers :: Blackbox :: Themes",
    "Topic :: Desktop Environment :: Window Managers :: CTWM",
    "Topic :: Desktop Environment :: Window Managers :: CTWM :: Themes",
    "Topic :: Desktop Environment :: Window Managers :: Enlightenment",
    "Topic :: Desktop Environment :: Window Managers :: Enlightenment :: Epplets",
    "Topic :: Desktop Environment :: Window Managers :: Enlightenment :: Themes DR15",
    "Topic :: Desktop Environment :: Window Managers :: Enlightenment :: Themes DR16",
    "Topic :: Desktop Environment :: Window Managers :: Enlightenment :: Themes DR17",
    "Topic :: Desktop Environment :: Window Managers :: FVWM",
    "Topic :: Desktop Environment :: Window Managers :: FVWM :: Themes",
    "Topic :: Desktop Environment :: Window Managers :: Fluxbox",
    "Topic :: Desktop Environment :: Window Managers :: Fluxbox :: Themes",
    "Topic :: Desktop Environment :: Window Managers :: IceWM",
    "Topic :: Desktop Environment :: Window Managers :: IceWM :: Themes",
    "Topic :: Desktop Environment :: Window Managers :: MetaCity",
    "Topic :: Desktop Environment :: Window Managers :: MetaCity :: Themes",
    "Topic :: Desktop Environment :: Window Managers :: Oroborus",
    "Topic :: Desktop Environment :: Window Managers :: Oroborus :: Themes",
    "Topic :: Desktop Environment :: Window Managers :: Sawfish",
    "Topic :: Desktop Environment :: Window Managers :: Sawfish :: Themes 0.30",
    "Topic :: Desktop Environment :: Window Managers :: Sawfish :: Themes pre-0.30",
    "Topic :: Desktop Environment :: Window Managers :: Waimea",
    "Topic :: Desktop Environment :: Window Managers :: Waimea :: Themes",
    "Topic :: Desktop Environment :: Window Managers :: Window Maker",
    "Topic :: Desktop Environment :: Window Managers :: Window Maker :: Applets",
    "Topic :: Desktop Environment :: Window Managers :: Window Maker :: Themes",
    "Topic :: Desktop Environment :: Window Managers :: XFCE",
    "Topic :: Desktop Environment :: Window Managers :: XFCE :: Themes",
    "Topic :: Documentation",
    "Topic :: Documentation :: Sphinx",
    "Topic :: Education",
    "Topic :: Education :: Computer Aided Instruction (CAI)",
    "Topic :: Education :: Testing",
    "Topic :: File Formats",
    "Topic :: File Formats :: JSON",
    "Topic :: File Formats :: JSON :: JSON Schema",
    "Topic :: Games/Entertainment",
    "Topic :: Games/Entertainment :: Arcade",
    "Topic :: Games/Entertainment :: Board Games",
    "Topic :: Games/Entertainment :: First Person Shooters",
    "Topic :: Games/Entertainment :: Fortune Cookies",
    "Topic :: Games/Entertainment :: Multi-User Dungeons (MUD)",
    "Topic :: Games/Entertainment :: Puzzle Games",
    "Topic :: Games/Entertainment :: Real Time Strategy",
    "Topic :: Games/Entertainment :: Role-Playing",
    "Topic :: Games/Entertainment :: Side-Scrolling/Arcade Games",
    "Topic :: Games/Entertainment :: Simulation",
    "Topic :: Games/Entertainment :: Turn Based Strategy",
    "Topic :: Home Automation",
    "Topic :: Internet",
    "Topic :: Internet :: File Transfer Protocol (FTP)",
    "Topic :: Internet :: Finger",
    "Topic :: Internet :: Log Analysis",
    "Topic :: Internet :: Name Service (DNS)",
    "Topic :: Internet :: Proxy Servers",
    "Topic :: Internet :: WAP",
    "Topic :: Internet :: WWW/HTTP",
    "Topic :: Internet :: WWW/HTTP :: Browsers",
    "Topic :: Internet :: WWW/HTTP :: Dynamic Content",
    "Topic :: Internet :: WWW/HTTP :: Dynamic Content :: CGI Tools/Libraries",
    "Topic :: Internet :: WWW/HTTP :: Dynamic Content :: Content Management System",
    "Topic :: Internet :: WWW/HTTP :: Dynamic Content :: Message Boards",
    "Topic :: Internet :: WWW/HTTP :: Dynamic Content :: News/Diary",
    "Topic :: Internet :: WWW/HTTP :: Dynamic Content :: Page Counters",
    "Topic :: Internet :: WWW/HTTP :: Dynamic Content :: Wiki",
    "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
    "Topic :: Internet :: WWW/HTTP :: Indexing/Search",
    "Topic :: Internet :: WWW/HTTP :: Session",
    "Topic :: Internet :: WWW/HTTP :: Site Management",
    "Topic :: Internet :: WWW/HTTP :: Site Management :: Link Checking",
    "Topic :: Internet :: WWW/HTTP :: WSGI",
    "Topic :: Internet :: WWW/HTTP :: WSGI :: Application",
    "Topic :: Internet :: WWW/HTTP :: WSGI :: Middleware",
    "Topic :: Internet :: WWW/HTTP :: WSGI :: Server",
    "Topic :: Internet :: XMPP",
    "Topic :: Internet :: Z39.50",
    "Topic :: Multimedia",
    "Topic :: Multimedia :: Graphics",
    "Topic :: Multimedia :: Graphics :: 3D Modeling",
    "Topic :: Multimedia :: Graphics :: 3D Rendering",
    "Topic :: Multimedia :: Graphics :: Capture",
    "Topic :: Multimedia :: Graphics :: Capture :: Digital Camera",
    "Topic :: Multimedia :: Graphics :: Capture :: Scanners",
    "Topic :: Multimedia :: Graphics :: Capture :: Screen Capture",
    "Topic :: Multimedia :: Graphics :: Editors",
    "Topic :: Multimedia :: Graphics :: Editors :: Raster-Based",
    "Topic :: Multimedia :: Graphics :: Editors :: Vector-Based",
    "Topic :: Multimedia :: Graphics :: Graphics Conversion",
    "Topic :: Multimedia :: Graphics :: Presentation",
    "Topic :: Multimedia :: Graphics :: Viewers",
    "Topic :: Multimedia :: Sound/Audio",
    "Topic :: Multimedia :: Sound/Audio :: Analysis",
    "Topic :: Multimedia :: Sound/Audio :: CD Audio",
    "Topic :: Multimedia :: Sound/Audio :: CD Audio :: CD Playing",
    "Topic :: Multimedia :: Sound/Audio :: CD Audio :: CD Ripping",
    "Topic :: Multimedia :: Sound/Audio :: CD Audio :: CD Writing",
    "Topic :: Multimedia :: Sound/Audio :: Capture/Recording",
    "Topic :: Multimedia :: Sound/Audio :: Conversion",
    "Topic :: Multimedia :: Sound/Audio :: Editors",
    "Topic :: Multimedia :: Sound/Audio :: MIDI",
    "Topic :: Multimedia :: Sound/Audio :: Mixers",
    "Topic :: Multimedia :: Sound/Audio :: Players",
    "Topic :: Multimedia :: Sound/Audio :: Players :: MP3",
    "Topic :: Multimedia :: Sound/Audio :: Sound Synthesis",
    "Topic :: Multimedia :: Sound/Audio :: Speech",
    "Topic :: Multimedia :: Video",
    "Topic :: Multimedia :: Video :: Capture",
    "Topic :: Multimedia :: Video :: Conversion",
    "Topic :: Multimedia :: Video :: Display",
    "Topic :: Multimedia :: Video :: Non-Linear Editor",
    "Topic :: Office/Business",
    "Topic :: Office/Business :: Financial",
    "Topic :: Office/Business :: Financial :: Accounting",
    "Topic :: Office/Business :: Financial :: Investment",
    "Topic :: Office/Business :: Financial :: Point-Of-Sale",
    "Topic :: Office/Business :: Financial :: Spreadsheet",
    "Topic :: Office/Business :: Groupware",
    "Topic :: Office/Business :: News/Diary",
    "Topic :: Office/Business :: Office Suites",
    "Topic :: Office/Business :: Scheduling",
    "Topic :: Other/Nonlisted Topic",
    "Topic :: Printing",
    "Topic :: Religion",
    "Topic :: Scientific/Engineering",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Scientific/Engineering :: Artificial Life",
    "Topic :: Scientific/Engineering :: Astronomy",
    "Topic :: Scientific/Engineering :: Atmospheric Science",
    "Topic :: Scientific/Engineering :: Bio-Informatics",
    "Topic :: Scientific/Engineering :: Chemistry",
    "Topic :: Scientific/Engineering :: Electronic Design Automation (EDA)",
    "Topic :: Scientific/Engineering :: GIS",
    "Topic :: Scientific/Engineering :: Human Machine Interfaces",
    "Topic :: Scientific/Engineering :: Hydrology",
    "Topic :: Scientific/Engineering :: Image Processing",
    "Topic :: Scientific/Engineering :: Image Recognition",
    "Topic :: Scientific/Engineering :: Information Analysis",
    "Topic :: Scientific/Engineering :: Interface Engine/Protocol Translator",
    "Topic :: Scientific/Engineering :: Mathematics",
    "Topic :: Scientific/Engineering :: Medical Science Apps.",
    "Topic :: Scientific/Engineering :: Oceanography",
    "Topic :: Scientific/Engineering :: Physics",
    "Topic :: Scientific/Engineering :: Quantum Computing",
    "Topic :: Scientific/Engineering :: Visualization",
    "Topic :: Security",
    "Topic :: Security :: Cryptography",
    "Topic :: Sociology",
    "Topic :: Sociology :: Genealogy",
    "Topic :: Sociology :: History",
    "Topic :: Software Development",
    "Topic :: Software Development :: Assemblers",
    "Topic :: Software Development :: Bug Tracking",
    "Topic :: Software Development :: Build Tools",
    "Topic :: Software Development :: Code Generators",
    "Topic :: Software Development :: Compilers",
    "Topic :: Software Development :: Debuggers",
    "Topic :: Software Development :: Disassemblers",
    "Topic :: Software Development :: Documentation",
    "Topic :: Software Development :: Embedded Systems",
    "Topic :: Software Development :: Embedded Systems :: Controller Area Network (CAN)",
    "Topic :: Software Development :: Embedded Systems :: Controller Area Network (CAN) :: CANopen",
    "Topic :: Software Development :: Embedded Systems :: Controller Area Network (CAN) :: J1939",
    "Topic :: Software Development :: Internationalization",
    "Topic :: Software Development :: Interpreters",
    "Topic :: Software Development :: Libraries",
    "Topic :: Software Development :: Libraries :: Application Frameworks",
    "Topic :: Software Development :: Libraries :: Java Libraries",
    "Topic :: Software Development :: Libraries :: PHP Classes",
    "Topic :: Software Development :: Libraries :: Perl Modules",
    "Topic :: Software Development :: Libraries :: Pike Modules",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Software Development :: Libraries :: Ruby Modules",
    "Topic :: Software Development :: Libraries :: Tcl Extensions",
    "Topic :: Software Development :: Libraries :: pygame",
    "Topic :: Software Development :: Localization",
    "Topic :: Software Development :: Object Brokering",
    "Topic :: Software Development :: Object Brokering :: CORBA",
    "Topic :: Software Development :: Pre-processors",
    "Topic :: Software Development :: Quality Assurance",
    "Topic :: Software Development :: Testing",
    "Topic :: Software Development :: Testing :: Acceptance",
    "Topic :: Software Development :: Testing :: BDD",
    "Topic :: Software Development :: Testing :: Mocking",
    "Topic :: Software Development :: Testing :: Traffic Generation",
    "Topic :: Software Development :: Testing :: Unit",
    "Topic :: Software Development :: User Interfaces",
    "Topic :: Software Development :: Version Control",
    "Topic :: Software Development :: Version Control :: Bazaar",
    "Topic :: Software Development :: Version Control :: CVS",
    "Topic :: Software Development :: Version Control :: Git",
    "Topic :: Software Development :: Version Control :: Mercurial",
    "Topic :: Software Development :: Version Control :: RCS",
    "Topic :: Software Development :: Version Control :: SCCS",
    "Topic :: Software Development :: Widget Sets",
    "Topic :: System",
    "Topic :: System :: Archiving",
    "Topic :: System :: Archiving :: Backup",
    "Topic :: System :: Archiving :: Compression",
    "Topic :: System :: Archiving :: Mirroring",
    "Topic :: System :: Archiving :: Packaging",
    "Topic :: System :: Benchmark",
    "Topic :: System :: Boot",
    "Topic :: System :: Boot :: Init",
    "Topic :: System :: Clustering",
    "Topic :: System :: Console Fonts",
    "Topic :: System :: Distributed Computing",
    "Topic :: System :: Emulators",
    "Topic :: System :: Filesystems",
    "Topic :: System :: Hardware",
    "Topic :: System :: Hardware :: Hardware Drivers",
    "Topic :: System :: Hardware :: Mainframes",
    "Topic :: System :: Hardware :: Symmetric Multi-processing",
    "Topic :: System :: Hardware :: Universal Serial Bus (USB)",
    "Topic :: System :: Hardware :: Universal Serial Bus (USB) :: Audio",
    "Topic :: System :: Hardware :: Universal Serial Bus (USB) :: Audio/Video (AV)",
    "Topic :: System :: Hardware :: Universal Serial Bus (USB) :: Communications Device Class (CDC)",
    "Topic :: System :: Hardware :: Universal Serial Bus (USB) :: Diagnostic Device",
    "Topic :: System :: Hardware :: Universal Serial Bus (USB) :: Hub",
    "Topic :: System :: Hardware :: Universal Serial Bus (USB) :: Human Interface Device (HID)",
    "Topic :: System :: Hardware :: Universal Serial Bus (USB) :: Mass Storage",
    "Topic :: System :: Hardware :: Universal Serial Bus (USB) :: Miscellaneous",
    "Topic :: System :: Hardware :: Universal Serial Bus (USB) :: Printer",
    "Topic :: System :: Hardware :: Universal Serial Bus (USB) :: Smart Card",
    "Topic :: System :: Hardware :: Universal Serial Bus (USB) :: Vendor",
    "Topic :: System :: Hardware :: Universal Serial Bus (USB) :: Video (UVC)",
    "Topic :: System :: Hardware :: Universal Serial Bus (USB) :: Wireless Controller",
    "Topic :: System :: Installation/Setup",
    "Topic :: System :: Logging",
    "Topic :: System :: Monitoring",
    "Topic :: System :: Networking",
    "Topic :: System :: Networking :: Firewalls",
    "Topic :: System :: Networking :: Monitoring",
    "Topic :: System :: Networking :: Monitoring :: Hardware Watchdog",
    "Topic :: System :: Networking :: Time Synchronization",
    "Topic :: System :: Operating System",
    "Topic :: System :: Operating System Kernels",
    "Topic :: System :: Operating System Kernels :: BSD",
    "Topic :: System :: Operating System Kernels :: GNU Hurd",
    "Topic :: System :: Operating System Kernels :: Linux",
    "Topic :: System :: Power (UPS)",
    "Topic :: System :: Recovery Tools",
    "Topic :: System :: Shells",
    "Topic :: System :: Software Distribution",
    "Topic :: System :: System Shells",
    "Topic :: System :: Systems Administration",
    "Topic :: System :: Systems Administration :: Authentication/Directory",
    "Topic :: System :: Systems Administration :: Authentication/Directory :: LDAP",
    "Topic :: System :: Systems Administration :: Authentication/Directory :: NIS",
    "Topic :: Terminals",
    "Topic :: Terminals :: Serial",
    "Topic :: Terminals :: Telnet",
    "Topic :: Terminals :: Terminal Emulators/X Terminals",
    "Topic :: Text Editors",
    "Topic :: Text Editors :: Documentation",
    "Topic :: Text Editors :: Emacs",
    "Topic :: Text Editors :: Integrated Development Environments (IDE)",
    "Topic :: Text Editors :: Text Processing",
    "Topic :: Text Editors :: Word Processors",
    "Topic :: Text Processing",
    "Topic :: Text Processing :: Filters",
    "Topic :: Text Processing :: Fonts",
    "Topic :: Text Processing :: General",
    "Topic :: Text Processing :: Indexing",
    "Topic :: Text Processing :: Linguistic",
    "Topic :: Text Processing :: Markup",
    "Topic :: Text Processing :: Markup :: HTML",
    "Topic :: Text Processing :: Markup :: LaTeX",
    "Topic :: Text Processing :: Markup :: Markdown",
    "Topic :: Text Processing :: Markup :: SGML",
    "Topic :: Text Processing :: Markup :: VRML",
    "Topic :: Text Processing :: Markup :: XML",
    "Topic :: Text Processing :: Markup :: reStructuredText",
    "Topic :: Utilities",
    "Typing :: Stubs Only",
    "Typing :: Typed",
]

classifiers: Set[str] = set(sorted_classifiers)


# A mapping from the deprecated classifier name to a list of zero or more valid
# classifiers that should replace it
deprecated_classifiers: Dict[str, List[str]] = {
    "Framework :: Django CMS :: 4.2": ["Framework :: Django CMS :: 5.0"],
    "License :: OSI Approved :: Intel Open Source License": [],
    "License :: OSI Approved :: Jabber Open Source License": [],
    "License :: OSI Approved :: MITRE Collaborative Virtual Workspace License (CVW)": [],
    "License :: OSI Approved :: Sun Industry Standards Source License (SISSL)": [],
    "License :: OSI Approved :: X.Net License": [],
    "Natural Language :: Ukranian": ["Natural Language :: Ukrainian"],
    "Topic :: Communications :: Chat :: AOL Instant Messenger": [],
}


# All classifiers, including deprecated classifiers
all_classifiers: List[str] = sorted(
    sorted_classifiers + list(deprecated_classifiers.keys())
)

__all__ = [
    "all_classifiers",
    "classifiers",
    "deprecated_classifiers",
    "sorted_classifiers",
]
