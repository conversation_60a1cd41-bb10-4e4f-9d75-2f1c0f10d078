<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 27.0.1, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 354.62 184.34" style="enable-background:new 0 0 354.62 184.34;" xml:space="preserve">
<style type="text/css">
	.st0{fill-rule:evenodd;clip-rule:evenodd;fill:#D9D9D9;}
	.st1{fill:none;stroke:#AFABAB;stroke-width:3;stroke-miterlimit:8;}
	.st2{fill-rule:evenodd;clip-rule:evenodd;}
	.st3{fill:none;stroke:#000000;stroke-miterlimit:8;}
	.st4{fill-rule:evenodd;clip-rule:evenodd;fill:#5B9BD5;}
	.st5{fill-rule:evenodd;clip-rule:evenodd;fill:#ED7D31;}
	.st6{fill:none;stroke:#000000;stroke-width:1;stroke-miterlimit:8;}
	.st7{font-family:'DejaVuSans';}
	.st8{font-size:18px;}
	.st9{fill:#ED7D31;}
	.st10{fill:#5B9BD5;}
</style>
<font horiz-adv-x="2048">
<!-- Copyright (c) 2003 by Bitstream, Inc. All Rights Reserved.
Copyright (c) 2006 by Tavmjong Bah. All Rights Reserved.
DejaVu changes are in public domain
 -->
<!-- Copyright: Copyright 2023 Adobe System Incorporated. All rights reserved. -->
<font-face font-family="DejaVuSans" units-per-em="2048" underline-position="-130" underline-thickness="90"/>
<missing-glyph horiz-adv-x="1229" d="M102,-362l0,1806l1024,0l0,-1806M217,-248l795,0l0,1577l-795,0z"/>
<glyph unicode="a" horiz-adv-x="1255" d="M702,563C553,563 450,546 393,512C336,478 307,420 307,338C307,273 329,221 372,183C415,144 473,125 547,125C649,125 731,161 793,234C854,306 885,402 885,522l0,41M1069,639l0,-639l-184,0l0,170C843,102 791,52 728,20C665,-13 589,-29 498,-29C383,-29 292,3 225,68C157,132 123,218 123,326C123,452 165,547 250,611C334,675 460,707 627,707l258,0l0,18C885,810 857,875 802,922C746,968 668,991 567,991C503,991 441,983 380,968C319,953 261,930 205,899l0,170C272,1095 338,1115 401,1128C464,1141 526,1147 586,1147C748,1147 869,1105 949,1021C1029,937 1069,810 1069,639z"/>
<glyph unicode="e" horiz-adv-x="1260" d="M1151,606l0,-90l-846,0C313,389 351,293 420,227C488,160 583,127 705,127C776,127 844,136 911,153C977,170 1043,196 1108,231l0,-174C1042,29 974,8 905,-7C836,-22 765,-29 694,-29C515,-29 374,23 270,127C165,231 113,372 113,549C113,732 163,878 262,986C361,1093 494,1147 662,1147C813,1147 932,1099 1020,1002C1107,905 1151,773 1151,606M967,660C966,761 938,841 883,901C828,961 755,991 664,991C561,991 479,962 418,904C356,846 320,764 311,659z"/>
<glyph unicode="l" horiz-adv-x="569" d="M193,1556l184,0l0,-1556l-184,0z"/>
<glyph unicode="m" horiz-adv-x="1995" d="M1065,905C1111,988 1166,1049 1230,1088C1294,1127 1369,1147 1456,1147C1573,1147 1663,1106 1726,1025C1789,943 1821,827 1821,676l0,-676l-185,0l0,670C1636,777 1617,857 1579,909C1541,961 1483,987 1405,987C1310,987 1234,955 1179,892C1124,829 1096,742 1096,633l0,-633l-185,0l0,670C911,778 892,858 854,910C816,961 757,987 678,987C584,987 509,955 454,892C399,828 371,742 371,633l0,-633l-185,0l0,1120l185,0l0,-174C413,1015 463,1065 522,1098C581,1131 650,1147 731,1147C812,1147 882,1126 939,1085C996,1044 1038,984 1065,905z"/>
<glyph unicode="n" horiz-adv-x="1298" d="M1124,676l0,-676l-184,0l0,670C940,776 919,855 878,908C837,961 775,987 692,987C593,987 514,955 457,892C400,829 371,742 371,633l0,-633l-185,0l0,1120l185,0l0,-174C415,1013 467,1064 527,1097C586,1130 655,1147 733,1147C862,1147 959,1107 1025,1028C1091,948 1124,831 1124,676z"/>
<glyph unicode="o" horiz-adv-x="1253" d="M627,991C528,991 450,953 393,876C336,799 307,693 307,559C307,425 336,320 393,243C450,166 528,127 627,127C725,127 803,166 860,243C917,320 946,426 946,559C946,692 917,797 860,875C803,952 725,991 627,991M627,1147C787,1147 913,1095 1004,991C1095,887 1141,743 1141,559C1141,376 1095,232 1004,128C913,23 787,-29 627,-29C466,-29 341,23 250,128C159,232 113,376 113,559C113,743 159,887 250,991C341,1095 466,1147 627,1147z"/>
<glyph unicode="r" horiz-adv-x="842" d="M842,948C821,960 799,969 775,975C750,980 723,983 694,983C590,983 510,949 455,882C399,814 371,717 371,590l0,-590l-185,0l0,1120l185,0l0,-174C410,1014 460,1065 522,1098C584,1131 659,1147 748,1147C761,1147 775,1146 790,1145C805,1143 822,1140 841,1137z"/>
<glyph unicode="s" horiz-adv-x="1067" d="M907,1087l0,-174C855,940 801,960 745,973C689,986 631,993 571,993C480,993 411,979 366,951C320,923 297,881 297,825C297,782 313,749 346,725C379,700 444,677 543,655l63,-14C737,613 830,574 885,523C940,472 967,400 967,309C967,205 926,123 844,62C761,1 648,-29 504,-29C444,-29 382,-23 317,-11C252,0 183,18 111,41l0,190C179,196 246,169 312,152C378,134 443,125 508,125C595,125 661,140 708,170C755,199 778,241 778,295C778,345 761,383 728,410C694,437 620,462 506,487l-64,15C328,526 246,563 195,613C144,662 119,730 119,817C119,922 156,1004 231,1061C306,1118 412,1147 549,1147C617,1147 681,1142 741,1132C801,1122 856,1107 907,1087z"/>
<glyph unicode="x" horiz-adv-x="1212" d="M1124,1120l-405,-545l426,-575l-217,0l-326,440l-326,-440l-217,0l435,586l-398,534l217,0l297,-399l297,399z"/>
<glyph unicode="y" horiz-adv-x="1212" d="M659,-104C607,-237 556,-324 507,-365C458,-406 392,-426 309,-426l-147,0l0,154l108,0C321,-272 360,-260 388,-236C416,-212 447,-155 481,-66l33,84l-453,1102l195,0l350,-876l350,876l195,0z"/>
<glyph unicode="z" horiz-adv-x="1075" d="M113,1120l874,0l0,-168l-692,-805l692,0l0,-147l-899,0l0,168l692,805l-667,0z"/>
</font>

	<path class="st0" d="M339.97,13.62c-42.83,10.14-95.25,26.35-113.05,33.78c-17.81,7.43-3.64,8.69,6.21,10.82
	c9.84,2.13,40-6.19,52.85,1.95c12.84,8.14,23.7,13.06,24.21,46.91c4.76,14.69-3.1,43.68,1.86,28.11
	C317.01,119.61,334.16,46.25,339.97,13.62z"/>
<path class="st1" d="M10.77,52.68c56.1-0.97,71.88-4.14,88.91,9.39c17.03,13.53,12.1,38.79,13.25,81.18"/>
<ellipse transform="matrix(0.0434 -0.9991 0.9991 0.0434 -20.3389 64.1638)" class="st2" cx="23.33" cy="42.7" rx="8.57" ry="8.57"/>
<ellipse transform="matrix(0.1602 -0.9871 0.9871 0.1602 -22.5539 58.8957)" class="st3" cx="23.33" cy="42.7" rx="8.57" ry="8.57"/>
<ellipse transform="matrix(0.9986 -0.0524 0.0524 0.9986 -2.6143 2.8633)" class="st2" cx="53.28" cy="51.27" rx="8.57" ry="8.57"/>
<ellipse transform="matrix(0.1602 -0.9871 0.9871 0.1602 -5.8642 95.6548)" class="st3" cx="53.28" cy="51.27" rx="8.57" ry="8.57"/>
<ellipse transform="matrix(0.8507 -0.5257 0.5257 0.8507 -14.3012 52.2048)" class="st2" cx="84.73" cy="51.27" rx="8.57" ry="8.57"/>
<ellipse transform="matrix(0.1602 -0.9871 0.9871 0.1602 20.5493 126.7002)" class="st3" cx="84.73" cy="51.27" rx="8.57" ry="8.57"/>
<circle class="st2" cx="104.36" cy="74.67" r="8.57"/>
<ellipse transform="matrix(0.1602 -0.9871 0.9871 0.1602 13.9373 165.7264)" class="st3" cx="104.36" cy="74.67" rx="8.57" ry="8.57"/>
<path class="st2" d="M110.27,97.45c0-4.73,3.84-8.57,8.57-8.57c4.73,0,8.57,3.84,8.57,8.57c0,4.73-3.84,8.57-8.57,8.57
	C114.11,106.02,110.27,102.18,110.27,97.45z"/>
<ellipse transform="matrix(0.1602 -0.9871 0.9871 0.1602 3.6159 199.1518)" class="st3" cx="118.85" cy="97.45" rx="8.57" ry="8.57"/>
<ellipse transform="matrix(0.1472 -0.9891 0.9891 0.1472 -33.1939 223.6739)" class="st2" cx="113.11" cy="131.09" rx="8.57" ry="8.57"/>
<ellipse transform="matrix(0.1602 -0.9871 0.9871 0.1602 -34.3992 221.7412)" class="st3" cx="113.11" cy="131.09" rx="8.57" ry="8.57"/>
<path class="st1" d="M209.04,57.36c56.1-0.97,71.88-4.14,88.91,9.39c17.03,13.53,12.1,38.79,13.25,81.18"/>
<ellipse transform="matrix(0.0416 -0.9991 0.9991 0.0416 165.0509 266.832)" class="st2" cx="221.61" cy="47.39" rx="8.57" ry="8.57"/>
<ellipse transform="matrix(0.1602 -0.9871 0.9871 0.1602 139.3342 258.5409)" class="st3" cx="221.61" cy="47.39" rx="8.57" ry="8.57"/>
<circle class="st2" cx="251.55" cy="55.96" r="8.57"/>
<ellipse transform="matrix(0.1602 -0.9871 0.9871 0.1602 156.0238 295.3)" class="st3" cx="251.55" cy="55.96" rx="8.57" ry="8.57"/>
<circle class="st2" cx="283.01" cy="55.96" r="8.57"/>
<ellipse transform="matrix(0.1602 -0.9871 0.9871 0.1602 182.4374 326.3454)" class="st3" cx="283.01" cy="55.96" rx="8.57" ry="8.57"/>
<ellipse transform="matrix(0.0825 -0.9966 0.9966 0.0825 198.5863 374.4144)" class="st2" cx="302.63" cy="79.36" rx="8.57" ry="8.57"/>
<ellipse transform="matrix(0.1602 -0.9871 0.9871 0.1602 175.8253 365.3716)" class="st3" cx="302.63" cy="79.36" rx="8.57" ry="8.57"/>
<ellipse transform="matrix(0.2298 -0.9732 0.9732 0.2298 144.8553 387.303)" class="st2" cx="317.12" cy="102.14" rx="8.57" ry="8.57"/>
<ellipse transform="matrix(0.1602 -0.9871 0.9871 0.1602 165.5039 398.797)" class="st3" cx="317.12" cy="102.14" rx="8.57" ry="8.57"/>
<circle class="st2" cx="311.38" cy="135.77" r="8.57"/>
<ellipse transform="matrix(0.1602 -0.9871 0.9871 0.1602 127.4887 421.3864)" class="st3" cx="311.38" cy="135.77" rx="8.57" ry="8.57"/>
<ellipse transform="matrix(0.0453 -0.999 0.999 0.0453 311.2939 353.8873)" class="st4" cx="340.8" cy="14.08" rx="8.57" ry="8.57"/>
<ellipse transform="matrix(0.1602 -0.9871 0.9871 0.1602 272.3145 348.2199)" class="st3" cx="340.8" cy="14.08" rx="8.57" ry="8.57"/>
<polygon class="st5" points="19.05,40.51 19.05,22.19 14.76,22.19 23.33,13.62 31.91,22.19 27.62,22.19 27.62,40.51 "/>
<polygon class="st3" points="19.05,40.51 19.05,22.19 14.76,22.19 23.33,13.62 31.91,22.19 27.62,22.19 27.62,40.51 "/>
<polygon class="st5" points="50.05,47.74 50.05,29.43 45.76,29.43 54.33,20.85 62.91,29.43 58.62,29.43 58.62,47.74 "/>
<polygon class="st3" points="50.05,47.74 50.05,29.43 45.76,29.43 54.33,20.85 62.91,29.43 58.62,29.43 58.62,47.74 "/>
<polygon class="st5" points="80.38,48.54 87.59,31.7 83.65,30.02 94.9,25.51 99.41,36.76 95.47,35.08 88.26,51.91 "/>
<polygon class="st6" points="80.38,48.54 87.59,31.7 83.65,30.02 94.9,25.51 99.41,36.76 95.47,35.08 88.26,51.91 "/>
<polygon class="st5" points="105.42,70.25 118.54,57.47 115.55,54.4 127.67,54.55 127.51,66.67 124.52,63.6 111.41,76.39 "/>
<polygon class="st3" points="105.42,70.25 118.54,57.47 115.55,54.4 127.67,54.55 127.51,66.67 124.52,63.6 111.41,76.39 "/>
<polygon class="st5" points="120.13,91.83 138.34,93.75 138.79,89.49 146.42,98.91 136.99,106.53 137.44,102.27 119.23,100.35 "/>
<polygon class="st6" points="120.13,91.83 138.34,93.75 138.79,89.49 146.42,98.91 136.99,106.53 137.44,102.27 119.23,100.35 "/>
<polygon class="st5" points="117.41,126.82 135.7,125.87 135.48,121.59 144.48,129.71 136.36,138.71 136.14,134.43 117.85,135.38 
	"/>
<polygon class="st3" points="117.41,126.82 135.7,125.87 135.48,121.59 144.48,129.71 136.36,138.71 136.14,134.43 117.85,135.38 
	"/>
<text transform="matrix(1 0 0 1 7.6888 176.9128)"><tspan x="0" y="0" class="st7 st8">xy</tspan><tspan x="21.27" y="0" class="st7 st8">z</tspan></text>
<text transform="matrix(1 0 0 1 67.1888 176.9128)"><tspan x="0" y="0" class="st9 st7 st8">n</tspan><tspan x="11.39" y="0" class="st9 st7 st8">o</tspan><tspan x="22.4" y="0" class="st9 st7 st8">r</tspan><tspan x="29.46" y="0" class="st9 st7 st8">m</tspan><tspan x="46.96" y="0" class="st9 st7 st8">a</tspan><tspan x="57.97" y="0" class="st9 st7 st8">l</tspan></text>
<text transform="matrix(1 0 0 1 206.5628 176.9128)"><tspan x="0" y="0" class="st7 st8">xy</tspan><tspan x="21.27" y="0" class="st7 st8">z</tspan></text>
<text transform="matrix(1 0 0 1 266.0628 176.9128)"><tspan x="0" y="0" class="st10 st7 st8">s</tspan><tspan x="9.38" y="0" class="st10 st7 st8">e</tspan><tspan x="20.49" y="0" class="st10 st7 st8">n</tspan><tspan x="31.88" y="0" class="st10 st7 st8">s</tspan><tspan x="41.26" y="0" class="st10 st7 st8">o</tspan><tspan x="52.25" y="0" class="st10 st7 st8">r</tspan></text>
</svg>
