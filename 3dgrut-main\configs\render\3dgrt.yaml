method: 3dgrt # 3dgrt or 3dgut
  
pipeline_type: reference
backward_pipeline_type: ${render.pipeline_type}Bwd
particle_kernel_degree: 4
particle_kernel_density_clamping: true
particle_kernel_min_response: 0.0113
particle_kernel_min_alpha: ${div:1.0,255.0}
particle_kernel_max_alpha: 0.99
particle_radiance_sph_degree: 3
primitive_type: instances
min_transmittance: 0.001
max_consecutive_bvh_update: 15
enable_normals: false
enable_hitcounts: true
enable_kernel_timings: false
