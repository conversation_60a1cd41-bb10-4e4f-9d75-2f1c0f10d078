# DIB-Render
This is the official inference code for:

#### Learning to Predict 3D Objects with an Interpolation-based Differentiable Renderer (NeurIPS 2019)

[<PERSON><PERSON><PERSON>](http://www.cs.toronto.edu/~wenzheng/), [<PERSON>\*](http://www.cs.toronto.edu/~jungao/), [<PERSON><PERSON>\*](http://www.cs.toronto.edu/~linghuan/), [<PERSON>\*](), [<PERSON><PERSON><PERSON><PERSON>](https://users.aalto.fi/~lehtinj7/), [<PERSON>](https://www.cs.toronto.edu/~j<PERSON><PERSON>/), [<PERSON><PERSON>](http://www.cs.toronto.edu/~fidler/)


**[[Paper](https://arxiv.org/abs/1908.01210)]  [[Project Page](https://nv-tlabs.github.io/DIB-R/)]**


**Note**: key functions from this work have also been ported to [Kaolin Library](https://github.com/NVIDIAGameWorks/kaolin), where they continue to be maintained. See [this example repo](https://github.com/nv-tlabs/DIB-R-Single-Image-3D-Reconstruction), as well as [this tutorial](https://github.com/NVIDIAGameWorks/kaolin/blob/master/examples/tutorial/dibr_tutorial.ipynb). Please cite our paper regardless of the implementation you end up using for your research.  

## Usage


### Install dependencies

This code requires PyTorch 1.1.0 and python 3+. Please install dependencies by
```bash
pip install -r requirments.txt
```

### Compile the DIB-Render
```bash
cd dib-render/cuda_dib_render
python build.py install
```


### Inference
``` bash
python test-all.py \
 --g_model_dir ./checkpoints/g_model.pth \
 --svfolder ./prediction \
 --data_folder ./dataset \
 --filelist ./test_list.txt
```

To get the evaluation IOU, please first download the tool [Binvox](https://www.patrickmin.com/binvox/) and install it's dependencies,

Voxelize the prediction using Binvox
```bash
python voxelization.py  --folder ./prediction
```

To evaluate the IOU, please first install binvox-rw-py following this [Link](https://github.com/dimatura/binvox-rw-py), then run the script
```bash
python check_iou.py --folder ./prediction  --gt_folder ./dataset 
```

To get the boundary F-score, please run the following script
```bash
python check_chamfer.py --folder ./prediction  --gt_folder ./dataset 
```

### Ciatation
If you use the code, please cite our paper:
```
@inproceedings{chen2019dibrender,
title={Learning to Predict 3D Objects with an Interpolation-based Differentiable Renderer},
author={Wenzheng Chen and Jun Gao and Huan Ling and Edward Smith and Jaakko Lehtinen and Alec Jacobson and Sanja Fidler},
booktitle={Advances In Neural Information Processing Systems},
year={2019}
}
```