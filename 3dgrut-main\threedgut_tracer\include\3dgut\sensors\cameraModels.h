// SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
// SPDX-License-Identifier: Apache-2.0
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#pragma once

#include <tiny-cuda-nn/vec.h>

namespace threedgut {

struct OpenCVPinholeProjectionParameters {
    tcnn::vec2 nearFar;
    tcnn::vec2 principalPoint;
    tcnn::vec2 focalLength;
    tcnn::vec<6> radialCoeffs;
    tcnn::vec2 tangentialCoeffs;
    tcnn::vec4 thinPrismCoeffs;
};

struct OpenCVFisheyeProjectionParameters {
    tcnn::vec2 principalPoint;
    tcnn::vec2 focalLength;
    tcnn::vec4 radialCoeffs;
    float maxAngle;
};

struct CameraModelParameters {
    enum ShutterType {
        RollingTopToBottomShutter,
        RollingLeftToRightShutter,
        RollingBottomToTopShutter,
        RollingRightToLeftShutter,
        GlobalShutter
    } shutterType = GlobalShutter;

    enum ModelType {
        OpenCVPinholeModel,
        OpenCVFisheyeModel,
        EmptyModel,
        Unsupported
    } modelType = EmptyModel;

    union {
        OpenCVPinholeProjectionParameters ocvPinholeParams;
        OpenCVFisheyeProjectionParameters ocvFisheyeParams;
    };
};

} // namespace threedgut
