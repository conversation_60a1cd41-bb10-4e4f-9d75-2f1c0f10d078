include_configs:
  - ../../train/nksr_refine/train.yaml
  - plane.yaml

name: 'shapenet/plane_nksr_refine'

voxel_size: 0.00375
kernel_dim: 16

interpolator:
  n_hidden: 2
  hidden_dim: 32

feature: 'normal'

structure_schedule:
  start_step: 10
  end_step: 100

finetune_kernel_sdf: true

# VAE config
args_ckpt: '--'
resolution: 512

vae_noise_step_min: 200
vae_noise_step_max: 400

batch_size: 8
accumulate_grad_batches: 1